-- Add file embeddings table for semantic search
CREATE TABLE IF NOT EXISTS "FileEmbedding" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "projectId" varchar(255) DEFAULT 'default' NOT NULL,
  "filePath" varchar(500) NOT NULL,
  "contentHash" varchar(64) NOT NULL,
  "embedding" text NOT NULL, -- JSON string of embedding vector
  "metadata" json,
  "createdAt" timestamp DEFAULT now() NOT NULL,
  "updatedAt" timestamp DEFAULT now() NOT NULL
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "file_embeddings_project_id_idx" ON "FileEmbedding" ("projectId");
CREATE UNIQUE INDEX IF NOT EXISTS "file_path_unique" ON "FileEmbedding" ("filePath");

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_file_embedding_updated_at 
    BEFORE UPDATE ON "FileEmbedding" 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
