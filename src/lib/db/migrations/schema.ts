import { pgTable, foreignKey, uuid, varchar, timestamp, json, unique, text, boolean, integer, index, real, type AnyPgColumn, primaryKey } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"



export const connection = pgTable("Connection", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: uuid().notNull(),
	provider: varchar({ length: 50 }).notNull(),
	status: varchar({ length: 50 }).default('pending').notNull(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	lastSyncedAt: timestamp({ mode: 'string' }),
	metadata: json(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "Connection_userId_User_id_fk"
		}).onDelete("cascade"),
]);

export const passwordResetTokens = pgTable("password_reset_tokens", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: uuid("user_id").notNull(),
	token: text().notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "password_reset_tokens_user_id_User_id_fk"
		}).onDelete("cascade"),
	unique("password_reset_tokens_token_unique").on(table.token),
]);

export const suggestion = pgTable("Suggestion", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	documentId: uuid().notNull(),
	documentCreatedAt: timestamp({ mode: 'string' }).notNull(),
	originalText: text().notNull(),
	suggestedText: text().notNull(),
	description: text(),
	isResolved: boolean().default(false).notNull(),
	userId: uuid().notNull(),
	createdAt: timestamp({ mode: 'string' }).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "Suggestion_userId_User_id_fk"
		}),
	foreignKey({
			columns: [table.documentId, table.documentCreatedAt],
			foreignColumns: [document.id, document.createdAt],
			name: "Suggestion_documentId_documentCreatedAt_Document_id_createdAt_f"
		}),
]);

export const user = pgTable("User", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	email: varchar({ length: 64 }).notNull(),
	password: varchar({ length: 64 }),
	name: varchar(),
	firstName: varchar(),
	lastName: varchar(),
	linkedin: varchar({ length: 255 }),
	provider: varchar().default('credentials'),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	isAnonymous: boolean().default(false),
	messageCount: integer().default(0),
	isAnonymous: boolean("is_anonymous").default(false).notNull(),
});

export const projectConnection = pgTable("ProjectConnection", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid().notNull(),
	provider: varchar({ length: 50 }).notNull(),
	providerProjectId: text().notNull(),
	providerProjectData: json(),
	types: json(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [project.id],
			name: "ProjectConnection_projectId_Project_id_fk"
		}).onDelete("cascade"),
]);

export const fileState = pgTable("FileState", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	chatId: uuid().notNull(),
	messageId: uuid(),
	files: json().notNull(),
	createdAt: timestamp({ mode: 'string' }).notNull(),
	dependencies: json().notNull(),
	version: integer().default(1),
	sqlQuery: text(),
	sqlStatus: varchar(),
	sqlError: text(),
	isBaseCacheVersion: boolean("is_base_cache_version").default(false),
	projectId: uuid(),
}, (table) => [
	index("fileState_chatId_idx").using("btree", table.chatId.asc().nullsLast().op("uuid_ops")),
	index("fileState_chatId_isBaseCacheVersion_idx").using("btree", table.chatId.asc().nullsLast().op("bool_ops"), table.isBaseCacheVersion.asc().nullsLast().op("bool_ops")),
	index("fileState_chatId_messageId_idx").using("btree", table.chatId.asc().nullsLast().op("uuid_ops"), table.messageId.asc().nullsLast().op("uuid_ops")),
	index("fileState_createdAt_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("fileState_isBaseCacheVersion_idx").using("btree", table.isBaseCacheVersion.asc().nullsLast().op("bool_ops")),
	index("fileState_messageId_idx").using("btree", table.messageId.asc().nullsLast().op("uuid_ops")),
	index("fileState_projectId_idx").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("fileState_version_idx").using("btree", table.version.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.chatId],
			foreignColumns: [chat.id],
			name: "FileState_chatId_Chat_id_fk"
		}),
	foreignKey({
			columns: [table.messageId],
			foreignColumns: [message.id],
			name: "FileState_messageId_Message_id_fk"
		}),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [project.id],
			name: "FileState_projectId_Project_id_fk"
		}),
]);

export const apkBuilds = pgTable("apk_builds", {
	id: uuid().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	status: text().notNull(),
	apkUrl: text("apk_url"),
	error: text(),
	metadata: json(),
});

export const subscription = pgTable("Subscription", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: varchar({ length: 255 }).notNull(),
	status: varchar({ length: 50 }).default('inactive').notNull(),
	planId: varchar({ length: 50 }).default('free').notNull(),
	subscriptionId: varchar({ length: 255 }),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	credits: integer().default(50).notNull(),
	creditsUsed: integer().default(0).notNull(),
	isActive: boolean().default(false),
	lemonSqueezyCustomerId: varchar(),
	lemonSqueezySubscriptionId: varchar(),
	lemonSqueezyOrderId: varchar(),
	stripeCustomerId: varchar(),
	stripeSubscriptionId: varchar(),
	stripePriceId: varchar(),
	stripeCurrentPeriodEnd: timestamp({ mode: 'string' }),
	resetDate: timestamp({ mode: 'string' }),
	lemonSqueezyVariantId: varchar(),
	provider: varchar(),
	stripeVariantId: timestamp({ mode: 'string' }),
	isDowngraded: boolean().default(false),
	metadata: json(),
}, (table) => [
	index("subscription_isActive_idx").using("btree", table.isActive.asc().nullsLast().op("bool_ops")),
	index("subscription_status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
	index("subscription_userId_idx").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	index("subscription_userId_updatedAt_idx").using("btree", table.userId.asc().nullsLast().op("text_ops"), table.updatedAt.asc().nullsLast().op("text_ops")),
	unique("Subscription_subscriptionId_unique").on(table.subscriptionId),
]);

export const projectChat = pgTable("ProjectChat", {
	projectId: uuid().notNull(),
	chatId: uuid().notNull(),
	isPrimary: boolean().default(true),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [project.id],
			name: "ProjectChat_projectId_Project_id_fk"
		}),
	foreignKey({
			columns: [table.chatId],
			foreignColumns: [chat.id],
			name: "ProjectChat_chatId_Chat_id_fk"
		}),
]);

export const deployments = pgTable("deployments", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: uuid().notNull(),
	platform: varchar().notNull(),
	version: varchar({ length: 20 }).notNull(),
	status: varchar().default('queued').notNull(),
	url: text(),
	buildId: uuid(),
	fileStateId: uuid(),
	metadata: json(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	projectId: uuid(),
	versionCode: integer(),
	slug: text().notNull(),
	error: text(),
}, (table) => [
	index("slug_idx").using("btree", table.slug.asc().nullsLast().op("text_ops")),
	index("slug_platform_idx").using("btree", table.slug.asc().nullsLast().op("text_ops"), table.platform.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "deployments_userId_User_id_fk"
		}),
	foreignKey({
			columns: [table.buildId],
			foreignColumns: [apkBuilds.id],
			name: "deployments_buildId_apk_builds_id_fk"
		}),
	foreignKey({
			columns: [table.fileStateId],
			foreignColumns: [fileState.id],
			name: "deployments_fileStateId_FileState_id_fk"
		}),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [project.id],
			name: "deployments_projectId_Project_id_fk"
		}),
	unique("slug_unique").on(table.slug),
]);

export const tokenConsumption = pgTable("TokenConsumption", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	model: varchar({ length: 50 }).notNull(),
	totalTimeToken: real().notNull(),
	promptTokens: integer().notNull(),
	completionTokens: integer().notNull(),
	totalTokens: integer().notNull(),
	chatId: uuid().notNull(),
	messageId: uuid().notNull(),
	userId: uuid().notNull(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	inputCost: real().notNull(),
	outputCost: real().notNull(),
	totalCost: real().notNull(),
	isAnonymous: boolean().default(false),
	remoteProvider: varchar(),
	remoteProviderId: varchar(),
	cachingDiscount: real(),
	subtotal: real(),
	cacheDiscountPercent: real(),
	projectId: uuid().notNull(),
	creditsConsumed: integer(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	discountedCredits: integer(),
	discountReason: varchar({ length: 50 }),
	errorId: varchar(),
	discounted: boolean().default(false),
	isAutoFixed: boolean().default(false),
}, (table) => [
	index("tokenConsumption_chatId_idx").using("btree", table.chatId.asc().nullsLast().op("uuid_ops")),
	index("tokenConsumption_isAnonymous_idx").using("btree", table.isAnonymous.asc().nullsLast().op("bool_ops")),
	index("tokenConsumption_projectId_idx").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("tokenConsumption_userId_createdAt_idx").using("btree", table.userId.asc().nullsLast().op("timestamp_ops"), table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("tokenConsumption_userId_idx").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "TokenConsumption_userId_User_id_fk"
		}).onDelete("cascade"),
]);

export const chat = pgTable("Chat", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	createdAt: timestamp({ mode: 'string' }).notNull(),
	updatedAt: timestamp({ mode: 'string' }).notNull(),
	title: text().notNull(),
	userId: uuid().notNull(),
	visibility: varchar().default('private').notNull(),
	connectionId: uuid(),
	supabaseProjectId: text(),
	supabaseAnonKey: text(),
	supabaseServiceKey: text(),
	projectId: uuid(),
	isInitialized: boolean().default(true),
	type: varchar().default('app').notNull(),
	designHtml: text(),
	designStatus: varchar(),
	isDesignApproved: boolean().default(false),
	needsContinuation: boolean().default(false),
}, (table) => [
	index("chat_projectId_idx").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("chat_updatedAt_idx").using("btree", table.updatedAt.asc().nullsLast().op("timestamp_ops")),
	index("chat_userId_idx").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	index("chat_userId_projectId_idx").using("btree", table.userId.asc().nullsLast().op("uuid_ops"), table.projectId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "Chat_userId_User_id_fk"
		}),
	foreignKey({
			columns: [table.connectionId],
			foreignColumns: [connection.id],
			name: "Chat_connectionId_Connection_id_fk"
		}),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [project.id],
			name: "Chat_projectId_Project_id_fk"
		}),
]);

export const stream = pgTable("Stream", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	chatId: uuid().notNull(),
	createdAt: timestamp({ mode: 'string' }).notNull(),
});

export const message = pgTable("Message", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	chatId: uuid().notNull(),
	role: varchar().notNull(),
	content: json().notNull(),
	createdAt: timestamp({ mode: 'string' }).notNull(),
	userId: uuid(),
	remoteProvider: varchar(),
	remoteProviderId: varchar(),
	projectId: uuid(),
	componentContexts: json(),
	autoFixed: boolean().default(false),
	hidden: boolean().default(false),
	finishReason: varchar(),
	parentUserMessageId: uuid(),
	parentAssistantMessageId: uuid(),
	isAssistantGroupHead: boolean().default(false),
	parts: json(),
	version: integer().default(1),
	attachments: json(),
}, (table) => [
	index("message_chatId_createdAt_idx").using("btree", table.chatId.asc().nullsLast().op("timestamp_ops"), table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("message_chatId_idx").using("btree", table.chatId.asc().nullsLast().op("uuid_ops")),
	index("message_parentAssistantMessageId_idx").using("btree", table.parentAssistantMessageId.asc().nullsLast().op("uuid_ops")),
	index("message_projectId_idx").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("message_role_idx").using("btree", table.role.asc().nullsLast().op("text_ops")),
	index("message_userId_idx").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.chatId],
			foreignColumns: [chat.id],
			name: "Message_chatId_Chat_id_fk"
		}),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "Message_userId_User_id_fk"
		}),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [project.id],
			name: "Message_projectId_Project_id_fk"
		}),
]);

export const screenshotState = pgTable("ScreenshotState", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: uuid(),
	chatId: uuid(),
	messageId: uuid(),
	screenshots: json().notNull(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("screenshotState_chatId_idx").using("btree", table.chatId.asc().nullsLast().op("uuid_ops")),
	index("screenshotState_createdAt_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("screenshotState_messageId_idx").using("btree", table.messageId.asc().nullsLast().op("uuid_ops")),
	index("screenshotState_projectId_idx").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [project.id],
			name: "ScreenshotState_projectId_Project_id_fk"
		}),
]);

export const designScreen = pgTable("DesignScreen", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	chatId: uuid().notNull(),
	projectId: uuid().notNull(),
	name: text().notNull(),
	html: text().notNull(),
	order: real().default(0).notNull(),
	status: varchar().default('complete').notNull(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("design_screen_chatId_idx").using("btree", table.chatId.asc().nullsLast().op("uuid_ops")),
	index("design_screen_order_idx").using("btree", table.order.asc().nullsLast().op("float4_ops")),
	index("design_screen_projectId_idx").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.chatId],
			foreignColumns: [chat.id],
			name: "DesignScreen_chatId_Chat_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [project.id],
			name: "DesignScreen_projectId_Project_id_fk"
		}).onDelete("cascade"),
]);

export const temperatureOptimization = pgTable("TemperatureOptimization", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	messageId: uuid().notNull(),
	chatId: uuid().notNull(),
	projectId: uuid(),
	userId: uuid(),
	optimizedTemperature: real().notNull(),
	selectedModel: varchar({ length: 100 }).notNull(),
	reasoning: text().notNull(),
	contextFactors: json().notNull(),
	optimizationDuration: integer(),
	wasSuccessful: boolean().default(true).notNull(),
	errorMessage: text(),
	usedFallback: boolean().default(false).notNull(),
	fallbackReason: varchar({ length: 100 }),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	userProgression: json(),
	fileCount: integer().default(0).notNull(),
}, (table) => [
	index("temperature_optimization_chatId_idx").using("btree", table.chatId.asc().nullsLast().op("uuid_ops")),
	index("temperature_optimization_createdAt_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("temperature_optimization_messageId_idx").using("btree", table.messageId.asc().nullsLast().op("uuid_ops")),
	index("temperature_optimization_projectId_idx").using("btree", table.projectId.asc().nullsLast().op("uuid_ops")),
	index("temperature_optimization_successful_idx").using("btree", table.wasSuccessful.asc().nullsLast().op("bool_ops")),
	index("temperature_optimization_temperature_idx").using("btree", table.optimizedTemperature.asc().nullsLast().op("float4_ops")),
	index("temperature_optimization_userId_idx").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.messageId],
			foreignColumns: [message.id],
			name: "TemperatureOptimization_messageId_Message_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.chatId],
			foreignColumns: [chat.id],
			name: "TemperatureOptimization_chatId_Chat_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [project.id],
			name: "TemperatureOptimization_projectId_Project_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "TemperatureOptimization_userId_User_id_fk"
		}).onDelete("cascade"),
]);

export const fileEmbedding = pgTable("FileEmbedding", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	projectId: varchar({ length: 255 }).default('default').notNull(),
	filePath: varchar({ length: 500 }).notNull(),
	contentHash: varchar({ length: 64 }).notNull(),
	embedding: text().notNull(),
	metadata: json(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("file_embeddings_project_id_idx").using("btree", table.projectId.asc().nullsLast().op("text_ops")),
	unique("file_path_unique").on(table.filePath),
]);

export const project = pgTable("Project", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	appName: text(),
	userId: uuid().notNull(),
	connectionId: uuid(),
	visibility: varchar().default('private').notNull(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	slug: text(),
	scheme: text(),
	bundleIdentifier: text(),
	packageName: text(),
	icon: text(),
	splashImage: text(),
	primaryColor: text().default('#000000'),
	description: text(),
	privacyPolicyUrl: text(),
	termsOfServiceUrl: text(),
	supabaseProjectId: text(),
	supabaseAnonKey: text(),
	supabaseServiceKey: text(),
	isMigratedv1: boolean().default(false),
	prompt: text(),
	initialUxGuidelines: text(),
	knowledgeBase: text(),
	aiGeneratedMemory: text(),
	approvedDesignHtml: text(),
	designChatId: uuid(),
	convexProjectId: text(),
	convexDeploymentUrl: text(),
	convexDeploymentKey: text(),
	convexTeam: text(),
	mongodbAtlasAppId: text(),
	mongodbAtlasUrl: text(),
	mongodbAtlasProjectId: text(),
	mongodbAtlasDatabaseName: text(),
	instantDbAppId: text(),
	instantDbAppTitle: text(),
	instantDbCreatedAt: timestamp({ mode: 'string' }),
	mongodbUsername: text(),
	mongodbPasswordEncrypted: text(),
	mongodbDatabaseName: text(),
	mongodbConnectionString: text(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "Project_userId_User_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.connectionId],
			foreignColumns: [connection.id],
			name: "Project_connectionId_Connection_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.designChatId],
			foreignColumns: [chat.id],
			name: "Project_designChatId_Chat_id_fk"
		}),
]);

export const oauthAuthorizationCodes = pgTable("oauth_authorization_codes", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	code: varchar({ length: 255 }).notNull(),
	clientId: varchar("client_id", { length: 255 }).notNull(),
	userId: uuid("user_id").notNull(),
	redirectUri: text("redirect_uri").notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	codeChallenge: varchar("code_challenge", { length: 255 }),
	codeChallengeMethod: varchar("code_challenge_method", { length: 10 }),
	state: varchar({ length: 255 }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("oauth_auth_codes_client_id_idx").using("btree", table.clientId.asc().nullsLast().op("text_ops")),
	index("oauth_auth_codes_code_idx").using("btree", table.code.asc().nullsLast().op("text_ops")),
	index("oauth_auth_codes_expires_at_idx").using("btree", table.expiresAt.asc().nullsLast().op("timestamp_ops")),
	index("oauth_auth_codes_user_id_idx").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "oauth_authorization_codes_user_id_User_id_fk"
		}).onDelete("cascade"),
	unique("oauth_authorization_codes_code_unique").on(table.code),
]);

export const oauthClients = pgTable("oauth_clients", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	clientId: varchar("client_id", { length: 255 }).notNull(),
	clientSecret: varchar("client_secret", { length: 255 }),
	appId: uuid("app_id"),
	redirectUri: text("redirect_uri").notNull(),
	name: varchar({ length: 255 }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("oauth_clients_app_id_idx").using("btree", table.appId.asc().nullsLast().op("uuid_ops")),
	index("oauth_clients_client_id_idx").using("btree", table.clientId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.appId],
			foreignColumns: [project.id],
			name: "oauth_clients_app_id_Project_id_fk"
		}).onDelete("cascade"),
	unique("oauth_clients_client_id_unique").on(table.clientId),
]);

export const oauthTokens = pgTable("oauth_tokens", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	accessToken: varchar("access_token", { length: 255 }).notNull(),
	refreshToken: varchar("refresh_token", { length: 255 }).notNull(),
	clientId: varchar("client_id", { length: 255 }).notNull(),
	userId: uuid("user_id").notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	scope: varchar({ length: 255 }).default('read write').notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("oauth_tokens_access_token_idx").using("btree", table.accessToken.asc().nullsLast().op("text_ops")),
	index("oauth_tokens_client_id_idx").using("btree", table.clientId.asc().nullsLast().op("text_ops")),
	index("oauth_tokens_expires_at_idx").using("btree", table.expiresAt.asc().nullsLast().op("timestamp_ops")),
	index("oauth_tokens_refresh_token_idx").using("btree", table.refreshToken.asc().nullsLast().op("text_ops")),
	index("oauth_tokens_user_id_idx").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "oauth_tokens_user_id_User_id_fk"
		}).onDelete("cascade"),
	unique("oauth_tokens_access_token_unique").on(table.accessToken),
	unique("oauth_tokens_refresh_token_unique").on(table.refreshToken),
]);

export const instantDbPlatformConfig = pgTable("instant_db_platform_config", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	clientId: text("client_id").notNull(),
	clientSecret: text("client_secret").notNull(),
	accessToken: text("access_token"),
	refreshToken: text("refresh_token"),
	tokenExpiresAt: timestamp("token_expires_at", { mode: 'string' }),
	isActive: boolean("is_active").default(false).notNull(),
	lastRefreshedAt: timestamp("last_refreshed_at", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const chatToProject = pgTable("ChatToProject", {
	chatId: uuid().notNull(),
	projectId: uuid().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.chatId],
			foreignColumns: [chat.id],
			name: "ChatToProject_chatId_Chat_id_fk"
		}),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [project.id],
			name: "ChatToProject_projectId_Project_id_fk"
		}),
	primaryKey({ columns: [table.chatId, table.projectId], name: "ChatToProject_chatId_projectId_pk"}),
]);

export const vote = pgTable("Vote", {
	chatId: uuid().notNull(),
	messageId: uuid().notNull(),
	isUpvoted: boolean().notNull(),
	projectId: uuid(),
}, (table) => [
	foreignKey({
			columns: [table.chatId],
			foreignColumns: [chat.id],
			name: "Vote_chatId_Chat_id_fk"
		}),
	foreignKey({
			columns: [table.messageId],
			foreignColumns: [message.id],
			name: "Vote_messageId_Message_id_fk"
		}),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [project.id],
			name: "Vote_projectId_Project_id_fk"
		}),
	primaryKey({ columns: [table.chatId, table.messageId], name: "Vote_chatId_messageId_pk"}),
]);

export const document = pgTable("Document", {
	id: uuid().defaultRandom().notNull(),
	createdAt: timestamp({ mode: 'string' }).notNull(),
	title: text().notNull(),
	content: text(),
	text: varchar().default('text').notNull(),
	userId: uuid().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "Document_userId_User_id_fk"
		}),
	primaryKey({ columns: [table.id, table.createdAt], name: "Document_id_createdAt_pk"}),
]);
