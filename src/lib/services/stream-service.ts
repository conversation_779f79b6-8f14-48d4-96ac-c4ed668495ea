import {
    CoreMessage,
    DataStreamWriter,
    FinishReason,
    Message as AIMessage,
    TextPart,
    generateText,
    smoothStream,
    streamText, LanguageModelResponseMetadata, CoreAssistantMessage, CoreToolMessage
} from "ai";
import {customModel} from "@/lib/ai";
import {fetchFromOpenRouter, getMessageDetailsFromOpenrouter} from "@/lib/openrouter/get-message-details";
import {round} from "lodash";

/**
 * Configuration options for the StreamService
 */
export interface StreamServiceConfig {
    // Core dependencies
    messages: CoreMessage[];
    parser: { parse: (messageId: string, text: string) => string };
    diffParser: { parse: (messageId: string, text: string) => void };
    dataStream: DataStreamWriter;

    // Request metadata
    userMessageId: string;
    abortSignal?: AbortSignal;

    // Feature flags
    agentModeEnabled?: boolean;
    isFirstMessage?: boolean;

    // Optional callbacks
    onFinish?: (params: StreamFinishParams) => Promise<void>;

    // Optional tools
    tools?: Record<string, any>;

    // Optional configurations
    temperature?: number;
    maxSteps?: number;
    generateMessageId?: () => string;

    enableValidation?: boolean;
    transformChunking?: "line" | "word"
    isSupabaseConnected?: boolean;
    enabledTools?: string[];
    modelId?: string;
    isConnectingSupabase?: boolean;
}

/**
 * Parameters passed to the onFinish callback
 */
export interface StreamFinishParams {
    response: LanguageModelResponseMetadata & {
        /**
         The response messages that were generated during the call.
         Response messages can be either assistant messages or tool messages.
         They contain a generated id.
         */
        readonly messages: Array<(CoreAssistantMessage | CoreToolMessage) & {
            /**
             Message ID generated by the AI SDK.
             */
            id: string;
        }>;
        /**
         Response body (available only for providers that use HTTP requests).
         */
        body?: unknown;
    };
    usage: {
        completionTokens: string | number;
        promptTokens: string | number;
        totalTokens: string | number;
    };
    experimental_providerMetadata: any;
    finishReason: FinishReason;
    warnings: any;
    text: string;
    reasoning: any;
    logprobs: any;
    enableValidation?: boolean;
}

/**
 * Interface for tool call cost tracking
 */
export interface ToolCallCost {
    id: string;
    model: string;
    tokens_prompt: number;
    tokens_completion: number;
    native_tokens_prompt?: number;
    native_tokens_completion?: number;
    total_cost: number;
    cache_discount?: number;
    provider_name?: string;
    timestamp: Date;
}

function randomIntFromInterval(min, max) { // min and max included
    return Math.floor(Math.random() * (max - min + 1) + min);
}

/**
 * A service for handling AI text streaming with consistent configuration
 */
export class StreamService {
    private config: StreamServiceConfig;
    private toolCallCosts: ToolCallCost[] = [];

    constructor(config: StreamServiceConfig) {
        this.config = config;
    }

    /**
     * Start the text streaming process
     * @returns The result of the streamText call
     */
    public startStream() {
        const {
            messages,
            parser,
            diffParser,
            dataStream,
            userMessageId,
            abortSignal,
            agentModeEnabled = false,
            isFirstMessage = false,
            onFinish,
            tools = {},
            temperature = 0,
            maxSteps = 25,
            generateMessageId = () => crypto.randomUUID(),
            isSupabaseConnected = false,
            enabledTools = [],
            enableValidation,
            transformChunking = "line",
            modelId,
            isConnectingSupabase
        } = this.config;
        let streamModelId = modelId
        if(!modelId) {
            streamModelId = "anthropic/claude-sonnet-4"
            if (!isFirstMessage) {
                if(agentModeEnabled) {
                    // streamModelId = 'anthropic/claude-3.7-sonnet';
                    streamModelId = 'anthropic/claude-sonnet-4';
                    // streamModelId = 'openai/gpt-4.1';
                } else {
                    streamModelId = "anthropic/claude-sonnet-4"
                }
            }
            if(isConnectingSupabase) {
                streamModelId = "anthropic/claude-sonnet-4"
            }
        }

        return streamText({
            model: customModel(streamModelId || 'anthropic/claude-sonnet-4'),
            maxSteps,
            experimental_activeTools: enabledTools,
            experimental_toolCallStreaming: true,
            toolCallStreaming: true,
            temperature,
            messages: messages.filter(m => !!m),
            experimental_transform: smoothStream({chunking: transformChunking}),
            maxRetries: 3,
            abortSignal,
            experimental_continueSteps: true,
            onError: (error) => {
                console.log('Error', error);
                throw error;
            },
            onStepFinish: async (stepResult) => {
                // console.log('stepResult', stepResult)
                const body = await fetchFromOpenRouter(stepResult.response.id);
                // console.log('Step result body', body)
                
                // Track tool call cost if data is available
                if (body && stepResult.response.id) {
                    const toolCallCost: ToolCallCost = {
                        id: stepResult.response.id,
                        model: body.data.model || streamModelId,
                        tokens_prompt: body.data.tokens_prompt || 0,
                        tokens_completion: body.data.tokens_completion || 0,
                        native_tokens_prompt: body.data.native_tokens_prompt,
                        native_tokens_completion: body.data.native_tokens_completion,
                        total_cost: body.data.total_cost || 0,
                        cache_discount: body.data.cache_discount,
                        provider_name: body.data.provider_name,
                        timestamp: new Date()
                    };
                    console.log('toolCallCost', toolCallCost)
                    this.toolCallCosts.push(toolCallCost);
                }
            },
            onChunk: ({chunk}) => {
                if (chunk.type === "text-delta") {
                    try {
                        // First run the file parser
                        const afterFileParser = parser.parse(userMessageId, chunk.textDelta);

                        // Then run the diff parser on the output of the file parser
                        diffParser.parse(userMessageId, afterFileParser);
                    } catch (e) {
                        console.error('Error in chunk processing', e);
                    }
                } else if (chunk.type === "tool-call") {
                    // Send tool call information to the client
                    dataStream.writeData({
                        type: 'tool-call',
                        content: {
                            toolCallId: chunk.toolCallId,
                            toolName: chunk.toolName,
                            args: chunk.args
                        }
                    });
                } else if (chunk.type === "tool-result") {
                    // Send tool result information to the client
                    dataStream.writeData({
                        type: 'tool-result',
                        content: {
                            toolCallId: chunk.toolCallId,
                            toolName: chunk.toolName,
                            result: chunk.result
                        }
                    });
                }
            },
            experimental_generateMessageId: generateMessageId,
            experimental_repairToolCall: async ({
                                                    toolCall,
                                                    tools,
                                                    error,
                                                    messages,
                                                    system,
                                                }) => {
                console.log('In repair', error);
                const result = await generateText({
                    model: customModel(streamModelId || 'anthropic/claude-sonnet-4'),
                    system,
                    messages: [
                        ...messages,
                        {
                            role: 'assistant',
                            content: [
                                {
                                    type: 'tool-call',
                                    toolCallId: toolCall.toolCallId,
                                    toolName: toolCall.toolName,
                                    args: toolCall.args,
                                },
                            ],
                        },
                        {
                            role: 'tool' as const,
                            content: [
                                {
                                    type: 'tool-result',
                                    toolCallId: toolCall.toolCallId,
                                    toolName: toolCall.toolName,
                                    result: error.message,
                                },
                            ],
                        },
                    ],
                    tools,
                });

                const newToolCall = result.toolCalls.find(
                    newToolCall => newToolCall.toolName === toolCall.toolName,
                );

                return newToolCall != null
                    ? {
                        toolCallType: 'function' as const,
                        toolCallId: toolCall.toolCallId,
                        toolName: toolCall.toolName,
                        args: JSON.stringify(newToolCall.args),
                    }
                    : null;
            },
            tools,
            onFinish: async (params) => {
                if (onFinish) {
                    await onFinish({...params, enableValidation});
                }
            },
        });
    }

    /**
     * Sanitize response messages by removing incomplete tool calls
     * @param messages The messages to sanitize
     * @returns Sanitized messages
     */
    public static sanitizeResponseMessages(messages: AIMessage[]): AIMessage[] {
        return messages.filter(message => {
            // Filter out messages that are just incomplete tool calls
            if (message.role === 'assistant' && Array.isArray(message.content)) {
                const hasOnlyToolCalls = message.content.every(
                    part => part.type === 'tool-call'
                );

                if (hasOnlyToolCalls) {
                    return false;
                }
            }
            return true;
        });
    }

    /**
     * Get the aggregated cost and token usage for all tool calls
     * @returns Summary of token usage and costs
     */
    public getToolCallCostSummary() {
        if (this.toolCallCosts.length === 0) {
            return {
                count: 0,
                total_tokens_prompt: 0,
                total_tokens_completion: 0,
                total_cost: 0,
                total_cache_discount: 0,
                net_cost: 0,
                tool_calls: []
            };
        }
        
        const total_tokens_prompt = this.toolCallCosts.reduce((sum, call) => sum + (call.tokens_prompt || 0), 0);
        const total_tokens_completion = this.toolCallCosts.reduce((sum, call) => sum + (call.tokens_completion || 0), 0);
        const total_cost = this.toolCallCosts.reduce((sum, call) => sum + (call.total_cost || 0), 0);
        const total_cache_discount = this.toolCallCosts.reduce((sum, call) => sum + (call.cache_discount || 0), 0);
        
        return {
            count: this.toolCallCosts.length,
            total_tokens_prompt: round(total_tokens_prompt, 0),
            total_tokens_completion: round(total_tokens_completion, 0),
            total_tokens: round(total_tokens_prompt + total_tokens_completion, 0),
            total_cost: round(total_cost, 6),
            total_cache_discount: round(total_cache_discount, 6),
            net_cost: round(total_cost + total_cache_discount, 6),
            tool_calls: this.toolCallCosts
        };
    }
    
    /**
     * Get a simple formatted string representation of the tool call costs
     * @returns Formatted string with cost summary
     */
    public getToolCallCostReport(): string {
        const summary = this.getToolCallCostSummary();
        
        if (summary.count === 0) {
            return "No tool calls recorded.";
        }
        
        return `Tool Call Summary:
- Total Calls: ${summary.count}
- Prompt Tokens: ${summary.total_tokens_prompt}
- Completion Tokens: ${summary.total_tokens_completion}
- Total Tokens: ${summary.total_tokens}
- Total Cost: $${summary.total_cost}
- Cache Discount: $${summary.total_cache_discount}
- Net Cost: $${summary.net_cost}`;
    }
    
    /**
     * Create a new instance with updated configuration
     * @param updatedConfig The updated configuration
     * @returns A new StreamService instance
     */
    public withConfig(updatedConfig: Partial<StreamServiceConfig>): StreamService {
        return new StreamService({
            ...this.config,
            ...updatedConfig,
        });
    }
}
