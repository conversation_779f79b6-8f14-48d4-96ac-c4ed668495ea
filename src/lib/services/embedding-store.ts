import { embedMany, embed, cosineSimilarity } from 'ai';
import { openai } from '@ai-sdk/openai';
import { FileItem } from '@/types/file';
import { db } from '@/lib/db';
import { fileEmbeddings } from '@/lib/db/schema';
import { eq, desc } from 'drizzle-orm';
import crypto from 'crypto';

interface ScoredFile {
  filePath: string;
  score: number;
  metadata?: {
    type: string;
    exports: string[];
    lineCount: number;
  };
}

/**
 * EmbeddingStore - Single responsibility: Manage file embeddings for semantic search
 *
 * - Stores embeddings in PostgreSQL for persistence
 * - Incrementally updates embeddings when files change
 * - Uses OpenAI embeddings via Vercel AI SDK
 * - Fast similarity search for file selection
 */
export class EmbeddingStore {
  private model = openai.embedding('text-embedding-3-small'); // Fast & cheap
  private projectId: string;

  constructor(projectId: string = 'default') {
    this.projectId = projectId;
  }

  /**
   * Update embeddings for changed files only
   */
  async updateEmbeddings(files: FileItem[]): Promise<void> {
    const filesToUpdate = await this.getFilesToUpdate(files);

    if (filesToUpdate.length === 0) {
      console.log('✅ All embeddings up to date');
      return;
    }

    console.log(`🔄 Updating ${filesToUpdate.length} file embeddings...`);

    try {
      await this.batchEmbed(filesToUpdate);
      await this.removeDeletedFiles(files);
      console.log(`✅ Updated ${filesToUpdate.length} embeddings`);
    } catch (error) {
      console.error('❌ Failed to update embeddings:', error);
      throw error;
    }
  }

  /**
   * Find most semantically similar files to query
   */
  async findSimilarFiles(query: string, maxFiles: number = 8): Promise<string[]> {
    // Check if we have any embeddings
    const count = await db
      .select({ count: fileEmbeddings.id })
      .from(fileEmbeddings)
      .where(eq(fileEmbeddings.projectId, this.projectId));

    if (count.length === 0) {
      throw new Error('No embeddings available. Call updateEmbeddings first.');
    }

    // Embed the query
    const { embedding: queryEmbedding } = await embed({
      model: this.model,
      value: this.prepareContent(query),
    });

    // Get all embeddings from database
    const allEmbeddings = await db
      .select()
      .from(fileEmbeddings)
      .where(eq(fileEmbeddings.projectId, this.projectId));

    // Calculate similarities
    const scores: ScoredFile[] = [];
    for (const fileEmb of allEmbeddings) {
      try {
        const embedding = JSON.parse(fileEmb.embedding);
        const similarity = cosineSimilarity(queryEmbedding, embedding);
        scores.push({
          filePath: fileEmb.filePath,
          score: similarity,
          metadata: fileEmb.metadata as any
        });
      } catch (error) {
        console.error(`Error parsing embedding for ${fileEmb.filePath}:`, error);
      }
    }

    // Sort by similarity and return top files
    scores.sort((a, b) => b.score - a.score);

    console.log('🎯 Top semantic matches:');
    scores.slice(0, maxFiles).forEach((item, i) => {
      console.log(`  ${i + 1}. ${item.filePath} (${item.score.toFixed(3)})`);
    });

    return scores.slice(0, maxFiles).map(s => s.filePath);
  }

  /**
   * Get embedding statistics
   */
  async getStats(): Promise<{ totalFiles: number; lastUpdated: Date | null }> {
    const result = await db
      .select({
        count: fileEmbeddings.id,
        lastUpdated: fileEmbeddings.updatedAt
      })
      .from(fileEmbeddings)
      .where(eq(fileEmbeddings.projectId, this.projectId))
      .orderBy(desc(fileEmbeddings.updatedAt))
      .limit(1);

    return {
      totalFiles: result.length,
      lastUpdated: result.length > 0 ? result[0].lastUpdated : null
    };
  }

  private async getFilesToUpdate(files: FileItem[]): Promise<FileItem[]> {
    const filesToUpdate: FileItem[] = [];

    for (const file of files) {
      const contentHash = this.hashContent(file.content || '');

      const existing = await db
        .select()
        .from(fileEmbeddings)
        .where(eq(fileEmbeddings.filePath, file.name))
        .limit(1);

      if (existing.length === 0 || existing[0].contentHash !== contentHash) {
        filesToUpdate.push(file);
      }
    }

    return filesToUpdate;
  }

  private async batchEmbed(files: FileItem[]): Promise<void> {
    const batchSize = 10; // Extremely conservative batch size to avoid token limits

    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize);

      // Prepare and validate content
      const validatedContent = batch.map(f => {
        const content = this.prepareContent(f.content || '');
        // Ensure content is not empty and has minimum length
        return content.trim().length > 0 ? content : 'Empty file';
      });

      // Skip batch if all content is invalid
      if (validatedContent.every(content => content === 'Empty file')) {
        console.log(`  📦 Batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(files.length/batchSize)} skipped (empty files)`);
        continue;
      }

      const { embeddings } = await embedMany({
        model: this.model,
        values: validatedContent,
      });

      // Store embeddings
      batch.forEach((file, index) => {
        const contentHash = this.hashContent(file.content || '');
        this.embeddings.set(file.name, {
          filePath: file.name,
          contentHash,
          embedding: embeddings[index],
          lastUpdated: new Date()
        });
      });

      console.log(`  📦 Batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(files.length/batchSize)} complete`);
    }
  }

  private prepareContent(content: string): string {
    // Very aggressive truncation for text-embedding-3-small (8191 token limit)
    const maxLength = 8000; // ~2000 tokens to be very safe

    if (content.length <= maxLength) {
      return content;
    }

    // Take beginning and end to preserve structure
    const half = Math.floor(maxLength / 2);
    return content.slice(0, half) + '\n\n[... truncated ...]\n\n' + content.slice(-half);
  }

  private hashContent(content: string): string {
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  private removeDeletedFiles(currentFiles: FileItem[]): void {
    const currentFilePaths = new Set(currentFiles.map(f => f.name));
    for (const [filePath] of this.embeddings) {
      if (!currentFilePaths.has(filePath)) {
        this.embeddings.delete(filePath);
      }
    }
  }
}
