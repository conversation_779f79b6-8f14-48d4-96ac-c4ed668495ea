import { embedMany, embed, cosineSimilarity } from 'ai';
import { openai } from '@ai-sdk/openai';
import { FileItem } from '@/types/file';
import crypto from 'crypto';

interface FileEmbedding {
  filePath: string;
  contentHash: string;
  embedding: number[];
  lastUpdated: Date;
}

interface ScoredFile {
  filePath: string;
  score: number;
}

/**
 * EmbeddingStore - Single responsibility: Manage file embeddings for semantic search
 * 
 * - Incrementally updates embeddings when files change
 * - Uses OpenAI embeddings via Vercel AI SDK
 * - Fast similarity search for file selection
 */
export class EmbeddingStore {
  private embeddings: Map<string, FileEmbedding> = new Map();
  private model = openai.embedding('text-embedding-3-small'); // Fast & cheap

  /**
   * Update embeddings for changed files only
   */
  async updateEmbeddings(files: FileItem[]): Promise<void> {
    const filesToUpdate = this.getFilesToUpdate(files);
    
    if (filesToUpdate.length === 0) {
      console.log('✅ All embeddings up to date');
      return;
    }

    console.log(`🔄 Updating ${filesToUpdate.length} file embeddings...`);
    
    try {
      await this.batchEmbed(filesToUpdate);
      this.removeDeletedFiles(files);
      console.log(`✅ Updated ${filesToUpdate.length} embeddings`);
    } catch (error) {
      console.error('❌ Failed to update embeddings:', error);
      throw error;
    }
  }

  /**
   * Find most semantically similar files to query
   */
  async findSimilarFiles(query: string, maxFiles: number = 8): Promise<string[]> {
    if (this.embeddings.size === 0) {
      throw new Error('No embeddings available. Call updateEmbeddings first.');
    }

    // Embed the query
    const { embedding: queryEmbedding } = await embed({
      model: this.model,
      value: this.prepareContent(query),
    });

    // Calculate similarities
    const scores: ScoredFile[] = [];
    for (const [filePath, fileEmb] of this.embeddings) {
      const similarity = cosineSimilarity(queryEmbedding, fileEmb.embedding);
      scores.push({ filePath, score: similarity });
    }

    // Sort by similarity and return top files
    scores.sort((a, b) => b.score - a.score);
    
    console.log('🎯 Top semantic matches:');
    scores.slice(0, maxFiles).forEach((item, i) => {
      console.log(`  ${i + 1}. ${item.filePath} (${item.score.toFixed(3)})`);
    });

    return scores.slice(0, maxFiles).map(s => s.filePath);
  }

  /**
   * Get embedding statistics
   */
  getStats(): { totalFiles: number; lastUpdated: Date | null } {
    const embeddings = Array.from(this.embeddings.values());
    return {
      totalFiles: embeddings.length,
      lastUpdated: embeddings.length > 0 
        ? new Date(Math.max(...embeddings.map(e => e.lastUpdated.getTime())))
        : null
    };
  }

  private getFilesToUpdate(files: FileItem[]): FileItem[] {
    return files.filter(file => {
      const contentHash = this.hashContent(file.content || '');
      const existing = this.embeddings.get(file.name);
      return !existing || existing.contentHash !== contentHash;
    });
  }

  private async batchEmbed(files: FileItem[]): Promise<void> {
    const batchSize = 10; // Extremely conservative batch size to avoid token limits

    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize);

      // Prepare and validate content
      const validatedContent = batch.map(f => {
        const content = this.prepareContent(f.content || '');
        // Ensure content is not empty and has minimum length
        return content.trim().length > 0 ? content : 'Empty file';
      });

      // Skip batch if all content is invalid
      if (validatedContent.every(content => content === 'Empty file')) {
        console.log(`  📦 Batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(files.length/batchSize)} skipped (empty files)`);
        continue;
      }

      const { embeddings } = await embedMany({
        model: this.model,
        values: validatedContent,
      });

      // Store embeddings
      batch.forEach((file, index) => {
        const contentHash = this.hashContent(file.content || '');
        this.embeddings.set(file.name, {
          filePath: file.name,
          contentHash,
          embedding: embeddings[index],
          lastUpdated: new Date()
        });
      });

      console.log(`  📦 Batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(files.length/batchSize)} complete`);
    }
  }

  private prepareContent(content: string): string {
    // Very aggressive truncation for text-embedding-3-small (8191 token limit)
    const maxLength = 8000; // ~2000 tokens to be very safe

    if (content.length <= maxLength) {
      return content;
    }

    // Take beginning and end to preserve structure
    const half = Math.floor(maxLength / 2);
    return content.slice(0, half) + '\n\n[... truncated ...]\n\n' + content.slice(-half);
  }

  private hashContent(content: string): string {
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  private removeDeletedFiles(currentFiles: FileItem[]): void {
    const currentFilePaths = new Set(currentFiles.map(f => f.name));
    for (const [filePath] of this.embeddings) {
      if (!currentFilePaths.has(filePath)) {
        this.embeddings.delete(filePath);
      }
    }
  }
}
