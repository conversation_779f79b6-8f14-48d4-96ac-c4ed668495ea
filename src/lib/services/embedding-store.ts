import { embedMany, embed, cosineSimilarity } from 'ai';
import { openai } from '@ai-sdk/openai';
import { FileItem } from '@/types/file';
import { db } from '@/lib/db';
import { fileEmbeddings } from '@/lib/db/schema';
import { eq, desc, and, inArray } from 'drizzle-orm';
import crypto from 'crypto';

interface ScoredFile {
  filePath: string;
  score: number;
  metadata?: {
    type: string;
    exports: string[];
    lineCount: number;
  };
}

/**
 * EmbeddingStore - Single responsibility: Manage file embeddings for semantic search
 *
 * - Stores embeddings in PostgreSQL for persistence
 * - Incrementally updates embeddings when files change
 * - Uses OpenAI embeddings via Vercel AI SDK
 * - Fast similarity search for file selection
 */
export class EmbeddingStore {
  private model = openai.embedding('text-embedding-3-small'); // Fast & cheap
  private projectId: string;

  constructor(projectId: string = 'default') {
    this.projectId = projectId;
  }

  /**
   * Update embeddings for changed files only
   */
  async updateEmbeddings(files: FileItem[]): Promise<void> {
    const filesToUpdate = await this.getFilesToUpdate(files);

    if (filesToUpdate.length === 0) {
      console.log('✅ All embeddings up to date');
      return;
    }

    console.log(`🔄 Updating ${filesToUpdate.length} file embeddings...`);

    try {
      await this.batchEmbed(filesToUpdate);
      await this.removeDeletedFiles(files);
      console.log(`✅ Updated ${filesToUpdate.length} embeddings`);
    } catch (error) {
      console.error('❌ Failed to update embeddings:', error);
      throw error;
    }
  }

  /**
   * Find most semantically similar files to query
   */
  async findSimilarFiles(query: string, maxFiles: number = 8): Promise<string[]> {
    // Check if we have any embeddings
    const count = await db
      .select({ count: fileEmbeddings.id })
      .from(fileEmbeddings)
      .where(eq(fileEmbeddings.projectId, this.projectId));

    if (count.length === 0) {
      throw new Error('No embeddings available. Call updateEmbeddings first.');
    }

    // Embed the query
    const { embedding: queryEmbedding } = await embed({
      model: this.model,
      value: this.prepareContent(query),
    });

    // Get all embeddings from database
    const allEmbeddings = await db
      .select()
      .from(fileEmbeddings)
      .where(eq(fileEmbeddings.projectId, this.projectId));

    // Calculate similarities
    const scores: ScoredFile[] = [];
    for (const fileEmb of allEmbeddings) {
      try {
        const embedding = JSON.parse(fileEmb.embedding);
        const similarity = cosineSimilarity(queryEmbedding, embedding);
        scores.push({
          filePath: fileEmb.filePath,
          score: similarity,
          metadata: fileEmb.metadata as any
        });
      } catch (error) {
        console.error(`Error parsing embedding for ${fileEmb.filePath}:`, error);
      }
    }

    // Sort by similarity and return top files
    scores.sort((a, b) => b.score - a.score);

    console.log('🎯 Top semantic matches:');
    scores.slice(0, maxFiles).forEach((item, i) => {
      console.log(`  ${i + 1}. ${item.filePath} (${item.score.toFixed(3)})`);
    });

    return scores.slice(0, maxFiles).map(s => s.filePath);
  }

  /**
   * Get embedding statistics
   */
  async getStats(): Promise<{ totalFiles: number; lastUpdated: Date | null }> {
    const result = await db
      .select({
        count: fileEmbeddings.id,
        lastUpdated: fileEmbeddings.updatedAt
      })
      .from(fileEmbeddings)
      .where(eq(fileEmbeddings.projectId, this.projectId))
      .orderBy(desc(fileEmbeddings.updatedAt))
      .limit(1);

    return {
      totalFiles: result.length,
      lastUpdated: result.length > 0 ? result[0].lastUpdated : null
    };
  }

  private async getFilesToUpdate(files: FileItem[]): Promise<FileItem[]> {
    console.time('getFilesToUpdate');
    console.log(`🔍 Checking ${files.length} files for updates...`);

    const filesToUpdate: FileItem[] = [];

    // Batch query: get all existing embeddings for this project in one go
    console.time('batch-existence-check');
    const filePaths = files.map(f => f.name);
    const existingEmbeddings = await db
      .select({
        filePath: fileEmbeddings.filePath,
        contentHash: fileEmbeddings.contentHash
      })
      .from(fileEmbeddings)
      .where(
        and(
          eq(fileEmbeddings.projectId, this.projectId),
          inArray(fileEmbeddings.filePath, filePaths)
        )
      );
    console.timeEnd('batch-existence-check');
    console.log(`📊 Found ${existingEmbeddings.length} existing embeddings`);

    // Create a map for O(1) lookup
    const existingMap = new Map(
      existingEmbeddings.map(e => [e.filePath, e.contentHash])
    );

    // Check which files need updates
    console.time('content-hash-comparison');
    for (const file of files) {
      const contentHash = this.hashContent(file.content || '');
      const existingHash = existingMap.get(file.name);

      if (!existingHash || existingHash !== contentHash) {
        filesToUpdate.push(file);
      }
    }
    console.timeEnd('content-hash-comparison');

    console.timeEnd('getFilesToUpdate');
    console.log(`✅ ${filesToUpdate.length} files need embedding updates`);

    return filesToUpdate;
  }

  private async batchEmbed(files: FileItem[]): Promise<void> {
    console.time('batchEmbed-total');
    const batchSize = 10; // Extremely conservative batch size to avoid token limits
    console.log(`🔄 Starting batch embedding for ${files.length} files (batch size: ${batchSize})`);

    for (let i = 0; i < files.length; i += batchSize) {
      const batchNum = Math.floor(i/batchSize) + 1;
      const totalBatches = Math.ceil(files.length/batchSize);
      const batch = files.slice(i, i + batchSize);

      console.log(`\n📦 Processing Batch ${batchNum}/${totalBatches}:`);
      console.log(`   Files: ${batch.map(f => f.name).join(', ')}`);
      console.time(`batch-${batchNum}-total`);

      // Prepare and validate content
      console.time(`batch-${batchNum}-content-prep`);
      const validatedContent = batch.map((f, idx) => {
        const content = this.prepareContent(f.content || '');
        const isValid = content.trim().length > 0;
        const result = isValid ? content : 'Empty file';
        console.log(`   ${f.name}: ${isValid ? 'valid' : 'EMPTY'} (${f.content?.length || 0} chars → ${result.length} chars)`);
        return result;
      });
      console.timeEnd(`batch-${batchNum}-content-prep`);

      // Skip batch if all content is invalid
      const emptyCount = validatedContent.filter(c => c === 'Empty file').length;
      if (validatedContent.every(content => content === 'Empty file')) {
        console.log(`   ⚠️ Batch ${batchNum}/${totalBatches} SKIPPED - all ${emptyCount} files empty`);
        console.timeEnd(`batch-${batchNum}-total`);
        continue;
      }
      console.log(`   📊 Content validation: ${batch.length - emptyCount} valid, ${emptyCount} empty`);

      // Generate embeddings
      console.time(`batch-${batchNum}-embedding`);
      console.log(`   🧠 Generating embeddings for ${validatedContent.length} items...`);
      const { embeddings } = await embedMany({
        model: this.model,
        values: validatedContent,
      });
      console.timeEnd(`batch-${batchNum}-embedding`);
      console.log(`   ✅ Generated ${embeddings.length} embeddings`);

      // Store embeddings in database
      console.time(`batch-${batchNum}-database`);
      let successCount = 0;
      let failureCount = 0;

      for (let j = 0; j < batch.length; j++) {
        const file = batch[j];
        const embedding = embeddings[j];
        const contentHash = this.hashContent(file.content || '');

        try {
          console.time(`db-insert-${file.name}`);
          await db
            .insert(fileEmbeddings)
            .values({
              projectId: this.projectId,
              filePath: file.name,
              contentHash,
              embedding: JSON.stringify(embedding),
              metadata: {
                type: file.language || 'unknown',
                exports: this.extractExports(file.content || ''),
                lineCount: file.content?.split('\n').length || 0,
              },
              createdAt: new Date(),
              updatedAt: new Date(),
            })
            .onConflictDoUpdate({
              target: [fileEmbeddings.filePath],
              set: {
                contentHash,
                embedding: JSON.stringify(embedding),
                metadata: {
                  type: file.language || 'unknown',
                  exports: this.extractExports(file.content || ''),
                  lineCount: file.content?.split('\n').length || 0,
                },
                updatedAt: new Date(),
              },
            });
          console.timeEnd(`db-insert-${file.name}`);
          console.log(`   ✅ ${file.name}: DB insert SUCCESS`);
          successCount++;
        } catch (error) {
          console.timeEnd(`db-insert-${file.name}`);
          console.error(`   ❌ ${file.name}: DB insert FAILED:`, error);
          failureCount++;
        }
      }
      console.timeEnd(`batch-${batchNum}-database`);

      console.log(`   📊 Batch ${batchNum} results: ${successCount} success, ${failureCount} failures`);
      console.timeEnd(`batch-${batchNum}-total`);
      console.log(`  📦 Batch ${batchNum}/${totalBatches} complete\n`);
    }

    console.timeEnd('batchEmbed-total');
    console.log(`🏁 Batch embedding completed for ${files.length} files`);
  }

  private prepareContent(content: string): string {
    // Very aggressive truncation for text-embedding-3-small (8191 token limit)
    const maxLength = 8000; // ~2000 tokens to be very safe

    if (content.length <= maxLength) {
      return content;
    }

    // Take beginning and end to preserve structure
    const half = Math.floor(maxLength / 2);
    return content.slice(0, half) + '\n\n[... truncated ...]\n\n' + content.slice(-half);
  }

  private hashContent(content: string): string {
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  private extractExports(content: string): string[] {
    const exports: string[] = [];
    const exportRegex = /export\s+(const|function|class|interface|type|default)\s+(\w+)/g;

    let match;
    while ((match = exportRegex.exec(content)) !== null) {
      exports.push(match[2]);
    }

    return exports;
  }

  private async removeDeletedFiles(currentFiles: FileItem[]): Promise<void> {
    const currentFilePaths = new Set(currentFiles.map(f => f.name));

    // Get all embeddings for this project
    const allEmbeddings = await db
      .select({ filePath: fileEmbeddings.filePath })
      .from(fileEmbeddings)
      .where(eq(fileEmbeddings.projectId, this.projectId));

    // Delete embeddings for files that no longer exist
    for (const embedding of allEmbeddings) {
      if (!currentFilePaths.has(embedding.filePath)) {
        await db
          .delete(fileEmbeddings)
          .where(eq(fileEmbeddings.filePath, embedding.filePath));
      }
    }
  }
}
