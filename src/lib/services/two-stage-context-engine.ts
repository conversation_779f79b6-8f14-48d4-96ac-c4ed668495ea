import { z } from "zod";
import { generateObject } from "ai";
import { customModel } from "@/lib/ai";
import { FileItem } from "@/types/file";
import { CodeSnippet } from "./context-engine";
import * as path from "path";
import {orderBy} from "lodash";

/**
 * Interface for snippet identification result from the second LLM
 */
interface SnippetIdentification {
  fileName: string;
  startLine: number;
  endLine: number;
  snippetType: string;
  snippetName: string;
  relevanceScore: number;
  reasoning: string;
}

/**
 * Two-stage LLM approach for context retrieval
 * 1. First LLM identifies relevant files
 * 2. Second LLM identifies relevant snippets within those files
 */
export class TwoStageLLMContextEngine {
  private files: FileItem[] = [];
  private fileIndex: Map<string, FileItem> = new Map();

  /**
   * Initialize with project files
   */
  constructor(files: FileItem[]) {
    this.files = files;
    this.buildIndex();
  }

  /**
   * Build an index of files for quick lookup
   */
  private buildIndex(): void {
    for (const file of this.files) {
      this.fileIndex.set(file.name, file);
    }
  }

  /**
   * Get a structured representation of the codebase optimized for cheap LLM understanding
   * Groups files logically and provides clear context for React Native + Supabase projects
   */
  private getCodebaseStructure(): string {
    // Build import relationships
    const importMap = new Map<string, Set<string>>();

    // Extract imports and build relationships
    this.files.forEach(file => {
      const imports = this.extractImports(file.content || "");
      imports.forEach(importPath => {
        const resolvedPath = this.resolveImportPath(importPath, file.name);
        if (resolvedPath) {
          if (!importMap.has(resolvedPath)) {
            importMap.set(resolvedPath, new Set());
          }
          importMap.get(resolvedPath)?.add(file.name);
        }
      });
    });

    // Group files by logical categories for better LLM understanding
    const fileGroups = this.groupFilesByCategory();

    // Build structured output
    const sections: string[] = [];

    // Add header
    sections.push("PROJECT STRUCTURE:");
    sections.push("================");

    // Process each category
    Object.entries(fileGroups).forEach(([category, files]) => {
      if (files.length > 0) {
        sections.push(`\n${category}:`);

        files.forEach(file => {
          const fileType = this.determineFileType(file.name, file.content || "");
          const exports = this.extractExports(file.content || "");
          const usageCount = importMap.get(file.name)?.size || 0;
          const usedByFiles = Array.from(importMap.get(file.name) || []).slice(0, 2); // Limit to 2 for space

          // Create clean, structured line
          let line = `  ${file.name}`;

          // Add type indicator
          if (fileType !== 'unknown') {
            line += ` [${fileType}]`;
          }

          // Add key exports (limit to 3 most important)
          const keyExports = this.getKeyExports(exports, fileType);
          if (keyExports.length > 0) {
            line += ` → ${keyExports.slice(0, 3).join(', ')}`;
            if (keyExports.length > 3) {
              line += ` +${keyExports.length - 3}more`;
            }
          }

          // Add usage information (count + specific files)
          if (usageCount > 0) {
            line += ` (${usageCount} refs`;
            if (usedByFiles.length > 0) {
              // Show just the file names without full paths for brevity
              const shortNames = usedByFiles.map(f => f.split('/').pop()).slice(0, 2);
              line += `: ${shortNames.join(', ')}`;
              if (usageCount > 2) {
                line += ` +${usageCount - 2}more`;
              }
            }
            line += ')';
          }

          sections.push(line);
        });
      }
    });

    return sections.join('\n');
  }

  /**
   * Group files into logical categories for better LLM understanding
   */
  private groupFilesByCategory(): Record<string, typeof this.files> {
    const groups: Record<string, typeof this.files> = {
      'App Entry': [],
      'Screens': [],
      'Components': [],
      'Navigation': [],
      'State/Stores': [],
      'Services': [],
      'Database/Supabase': [],
      'Utils/Helpers': [],
      'Types': [],
      'Config': [],
      'Other': []
    };

    this.files.forEach(file => {
      const path = file.name.toLowerCase();
      const name = file.name.toLowerCase();

      // App entry points
      if (name.includes('app.') || name.includes('index.') || name === 'app.tsx' || name === 'app.ts') {
        groups['App Entry'].push(file);
      }
      // Screens
      else if (path.includes('screen') || path.includes('page') || name.endsWith('screen.tsx')) {
        groups['Screens'].push(file);
      }
      // Navigation
      else if (path.includes('navigation') || path.includes('navigator') || name.includes('navigation')) {
        groups['Navigation'].push(file);
      }
      // Components
      else if (path.includes('component') || name.endsWith('.tsx') && !path.includes('screen')) {
        groups['Components'].push(file);
      }
      // State management
      else if (path.includes('store') || path.includes('context') || name.includes('store') || name.includes('context')) {
        groups['State/Stores'].push(file);
      }
      // Services
      else if (path.includes('service') || path.includes('api') || name.includes('service')) {
        groups['Services'].push(file);
      }
      // Database/Supabase
      else if (path.includes('supabase') || path.includes('database') || path.includes('db') || name.includes('supabase')) {
        groups['Database/Supabase'].push(file);
      }
      // Types
      else if (name.includes('type') || name.includes('.d.ts') || path.includes('types')) {
        groups['Types'].push(file);
      }
      // Utils
      else if (path.includes('util') || path.includes('helper') || name.includes('util') || name.includes('helper')) {
        groups['Utils/Helpers'].push(file);
      }
      // Config
      else if (name.includes('config') || name.includes('.config.') || path.includes('config')) {
        groups['Config'].push(file);
      }
      // Everything else
      else {
        groups['Other'].push(file);
      }
    });

    // Sort files within each group by importance (usage and name)
    Object.keys(groups).forEach(category => {
      groups[category].sort((a, b) => {
        // Prioritize index files and main entry points
        if (a.name.includes('index.') && !b.name.includes('index.')) return -1;
        if (!a.name.includes('index.') && b.name.includes('index.')) return 1;

        // Then by name
        return a.name.localeCompare(b.name);
      });
    });

    return groups;
  }

  /**
   * Extract key exports, filtering noise for better LLM focus
   */
  private getKeyExports(exports: string[], fileType: string): string[] {
    // Filter out common noise
    const filtered = exports.filter(exp =>
      exp !== 'default' &&
      exp.length > 2 && // Skip very short names
      !exp.toLowerCase().includes('props') &&
      !exp.toLowerCase().includes('style')
    );

    // For components, prioritize component names
    if (fileType === 'component' || fileType === 'screen') {
      return filtered.filter(exp =>
        !exp.toLowerCase().startsWith('use') || exp.length > 6 // Keep longer hooks
      );
    }

    return filtered.slice(0, 5); // Limit to 5 key exports
  }

  /**
   * First stage: Find relevant files for a query
   */
  async findRelevantFiles(query: string, reason: string, excludedFiles: string[] = []): Promise<{files: string[], reasoning: string }> {
    console.time('find-relevant-files');

    // Get a compact representation of the codebase structure
    const codebaseStructure = this.getCodebaseStructure();

    // console.log('codebaseStructure', codebaseStructure)

    // Use LLM to identify relevant files
    const result = await generateObject({
      model: customModel('openai/gpt-4.1-mini'), // Using a smaller model to reduce costs
      temperature: 0.1,
      schema: z.object({
        files: z.array(z.string()),
        reasoning: z.string().describe("Explanation of why these files were selected")
      }),
      system: `You are a code analysis expert. Your task is to identify which files in a React Native Expo project are most relevant to a specific query.

You will be given:
1. A query about the codebase
2. A structured representation of files organized by category

HOW TO READ THE FILE FORMAT:
- Files are grouped by category (App Entry, Screens, Components, Services, etc.)
- Each file line shows: filename [type] → exports (usage_info)
- [type] indicates file purpose: [component], [hook], [service], [context], [config], etc.
- → exports shows key functions/classes exported by the file
- (N refs: file1, file2 +Nmore) shows which files import/use this file
- Higher ref counts indicate more important/central files
- Files with many references are often core infrastructure

SELECTION STRATEGY:
1. For architectural queries: Focus on files with high ref counts and core types
2. For specific features: Look for files in relevant categories with matching exports
3. For debugging: Include both the problematic area AND its dependencies (ref relationships)
4. Always include some high-ref-count files for context, even if not directly related

Return a JSON object with:
1. An array of the most relevant file paths (maximum 20)
2. Your reasoning for selecting these files

Choose files that would be most helpful for understanding or implementing the query.`,
      prompt: `Query: ${query}
      
Reason: ${reason}

Files in the project:
${codebaseStructure}

${excludedFiles.length > 0 ? `Note: The following files have been excluded from analysis: ${excludedFiles.join(', ')}\n\n` : ''}
Return the most relevant file paths (maximum 20) and your reasoning:`,
    });

    console.timeEnd('find-relevant-files');
    console.log(`Selected files reasoning: ${result.object.reasoning}`);

    console.log('result.object.files', result.object.files)
    // Filter out any files that don't exist in our index
    return {files: result.object.files.filter(file => this.fileIndex.has(file)), reasoning: result.object.reasoning};
  }

  /**
   * Second stage: Identify relevant snippets within files
   */
  async identifyRelevantSnippets(query: string, originalReason: string, relevantFiles: string[], previousStageReason: string): Promise<SnippetIdentification[]> {
    console.time('identify-snippets');


    // Prepare file contents with line numbers
    const fileContents = relevantFiles.map(fileName => {
      const file = this.fileIndex.get(fileName);
      const content = file?.content || "";

      // Add line numbers to help the LLM identify specific ranges
      // Format with consistent padding to make line numbers stand out
      const lines = content.split("\n");
      const maxLineNumberWidth = String(lines.length).length;
      const numberedContent = lines.map((line, i) => {
        const lineNumber = i + 1;
        const paddedLineNumber = String(lineNumber).padStart(maxLineNumberWidth, ' ');
        return `${paddedLineNumber}: ${line}`;
      }).join("\n");

      return {
        name: fileName,
        content: numberedContent,
        lineCount: lines.length
      };
    });

    // Use LLM to identify relevant snippets within these files
    const result = await generateObject({
      model: customModel('openai/gpt-4.1-mini'), // Using a more capable model for this task
      temperature: 0.1,
      schema: z.object({
        snippets: z.array(z.object({
          fileName: z.string(),
          startLine: z.number(),
          endLine: z.number(),
          snippetType: z.string(),
          snippetName: z.string(),
          relevanceScore: z.number().min(0).max(1),
          reasoning: z.string()
        }))
      }),
      system: `You are a code analysis expert. Your task is to identify the MINIMAL relevant code snippets within files for a specific query.

Return the exact line numbers for each snippet, along with metadata about the snippet.

Guidelines for MINIMAL context extraction:
1. Extract ONLY the specific lines directly relevant to the query - avoid including entire files or anything extra at all
2. For imports, include ONLY the imports directly related to the query and is needed to reliable editing
2. For style, include ONLY the styles directly related to the query and is needed to reliable editing
3. For components, include ONLY the component declaration, props, and relevant JSX elements - NOT the entire implementation
4. For functions, include ONLY the signature and critical logic - NOT entire function bodies
5. Assign a relevance score from 0.0 to 1.0 based on how directly the snippet addresses the query. 
6. Keep snippets as SHORT as possible while maintaining necessary context
7. Pay close attention to the line numbers at the beginning of each line (formatted as "NUMBER: code")
8. For React errors, focus on component declarations, imports/exports, and JSX return statements
9. NEVER include style definitions unless they are directly relevant to the query
10. NEVER include helper functions unless they are directly relevant to the query
11. When specific line numbers are requested, return only those line numbers

Token efficiency guidelines:
1. Maximum 30 lines per snippet unless absolutely necessary
4. Omit implementation details of methods unless directly relevant
5. For UI issues, include only the relevant JSX elements, not entire render methods
6. When multiple similar components exist, include only one representative example\``,
      prompt: `Query: ${query}

I need to identify the MINIMAL relevant code snippets in these files. For each relevant code block, provide:
1. The file name
2. Start and end line numbers (inclusive) - use the exact line numbers shown at the beginning of each line
3. Type of snippet (function, component, hook, type, etc.)
4. Name of the snippet (function name, component name, etc.)
5. Relevance score (0.0 to 1.0)
6. Brief reasoning for why this snippet is relevant

Reliable editing:
- Please include import and styles if they need are needed to add imports
- Include just enough context for clear understanding and editing 

CRITICAL TOKEN EFFICIENCY GUIDELINES:
- Extract ONLY the specific lines directly relevant to the query
- Each line in the files is prefixed with its line number (e.g., "42: const foo = bar;"). Use these exact line numbers.
- For imports, include ONLY those directly related to the query
- For components, include ONLY the declaration, props, and relevant JSX - NOT entire implementations
- NEVER include style definitions unless directly relevant to the query
- Keep snippets to a MAXIMUM of 30 lines when possible
- For React errors, focus ONLY on component declarations, imports/exports, and JSX return statements
- AVOID including entire component implementations - be extremely selective

Reasoning from the user/llm issuing the original query on why they want the result. This gets fed to the first engine:
${originalReason}

Reasoning from the first engine on why each file was selected. Use the reasoning to understand the whole flow and to select snippets accordingly:
${previousStageReason}

Files to analyze:
${fileContents.map(file => `
=== ${file.name} (${file.lineCount} lines) ===
${file.content}
`).join("\n\n")}

Return an array of the most relevant code snippets with their exact line numbers and metadata:`,
    });

    console.timeEnd('identify-snippets');
    return result.object.snippets;
  }

  /**
   * Smart truncation to fit within line budget while preserving understanding context
   */
  smartTruncateSnippets(snippets: CodeSnippet[], maxLines: number = 600): {truncatedSnippets: CodeSnippet[], additionalFromTruncation: {fileName: string, reason: string, suggestedQuery: string}[]} {
    // Categorize snippets by type for strategic selection
    const implementation = snippets.filter(s => (s.score || 0) >= 0.9);
    const usage = snippets.filter(s => (s.score || 0) >= 0.7 && (s.score || 0) < 0.9);
    const context = snippets.filter(s => (s.score || 0) < 0.7);

    let currentLines = 0;
    const truncatedSnippets: CodeSnippet[] = [];
    const additionalFromTruncation: {fileName: string, reason: string, suggestedQuery: string}[] = [];

    // Strategy: Always include implementation, then usage, then context within budget
    const prioritizedSnippets = [
      ...orderBy(implementation, ['score'], ['desc']).slice(0, 2), // Max 2 implementation snippets
      ...orderBy(usage, ['score'], ['desc']).slice(0, 2), // Max 2 usage examples
      ...orderBy(context, ['score'], ['desc']).slice(0, 1)  // Max 1 context snippet
    ];

    for (const snippet of prioritizedSnippets) {
      const snippetLines = snippet.content.split('\n').length;

      if (currentLines + snippetLines <= maxLines) {
        truncatedSnippets.push(snippet);
        currentLines += snippetLines;
      } else {
        // Add to additional files instead of truncating content
        additionalFromTruncation.push({
          fileName: snippet.filePath,
          reason: `Relevant but truncated due to 1000-line budget (relevance: ${(snippet.score || 0).toFixed(2)}, ${snippetLines} lines)`,
          suggestedQuery: `Show ${snippet.symbols.join(', ')} in ${snippet.filePath}`
        });
      }
    }

    // Add any remaining snippets to additional files
    const remainingSnippets = snippets.filter(s => !truncatedSnippets.includes(s) && !additionalFromTruncation.some(a => a.fileName === s.filePath));
    for (const snippet of remainingSnippets) {
      additionalFromTruncation.push({
        fileName: snippet.filePath,
        reason: `Additional context available (relevance: ${(snippet.score || 0).toFixed(2)})`,
        suggestedQuery: `Show ${snippet.symbols.join(', ')} in ${snippet.filePath}`
      });
    }

    console.log(`Smart truncation: kept ${truncatedSnippets.length}/${snippets.length} snippets within ${maxLines} line budget (${currentLines} lines total)`);
    console.log(`  - Implementation: ${implementation.length} found, ${truncatedSnippets.filter(s => (s.score || 0) >= 0.9).length} kept`);
    console.log(`  - Usage: ${usage.length} found, ${truncatedSnippets.filter(s => (s.score || 0) >= 0.7 && (s.score || 0) < 0.9).length} kept`);
    console.log(`  - Context: ${context.length} found, ${truncatedSnippets.filter(s => (s.score || 0) < 0.7).length} kept`);

    return {truncatedSnippets, additionalFromTruncation};
  }

  /**
   * Extract actual code snippets based on line numbers
   */
  extractSnippets(snippetIdentifications: SnippetIdentification[]): CodeSnippet[] {
    // Group snippets by file to avoid duplicate processing
    const snippetsByFile = new Map<string, SnippetIdentification[]>();

    for (const snippet of snippetIdentifications) {
      if (!snippetsByFile.has(snippet.fileName)) {
        snippetsByFile.set(snippet.fileName, []);
      }
      snippetsByFile.get(snippet.fileName)?.push(snippet);
    }

    const results: CodeSnippet[] = [];

    // Process each file's snippets
    for (const [fileName, fileSnippets] of snippetsByFile.entries()) {
      const file = this.fileIndex.get(fileName);
      if (!file || !file.content) continue;

      const lines = file.content.split("\n");

      // Find import statements (usually at the top of the file)
      const importEndLine = this.findImportEndLine(lines);
      const hasImports = importEndLine > 0;

      // Process each snippet in the file
      for (const identification of fileSnippets) {
        // Ensure line numbers are within bounds
        const startLine = Math.max(1, Math.min(identification.startLine, lines.length));
        const endLine = Math.max(startLine, Math.min(identification.endLine, lines.length));

        // Removing as this is causing the llm issues to understand
        const shouldIncludeImports = false;

        // Determine if we should include imports
        // const shouldIncludeImports = hasImports &&
        //     identification.snippetType.toLowerCase() !== 'import' &&
        //     startLine > importEndLine;

        // Extract the snippet content with imports if needed
        let snippetLines: string[];
        let actualStartLine: number;

        if (shouldIncludeImports) {
          // Include imports and the actual snippet
          const importLines = lines.slice(0, importEndLine);
          const codeLines = lines.slice(startLine - 1, endLine);

          // Add a separator between imports and code
          snippetLines = [...importLines, '', '// Main snippet', ...codeLines];
          actualStartLine = 1; // Starting from the beginning of the file
        } else {
          // Just include the snippet itself
          snippetLines = lines.slice(startLine - 1, endLine);
          actualStartLine = startLine;
        }

        const content = snippetLines.join("\n");

        // Log the extraction for debugging
        console.log(`Extracting snippet from ${identification.fileName}:`);
        console.log(`  Lines ${startLine}-${endLine} (${snippetLines.length} lines)`);
        console.log(`  Type: ${identification.snippetType}, Name: ${identification.snippetName}`);
        console.log(`  Relevance: ${identification.relevanceScore.toFixed(2)}`);
        if (shouldIncludeImports) {
          console.log(`  Including imports from lines 1-${importEndLine}`);
        }

        results.push({
          filePath: identification.fileName,
          content,
          startLine: actualStartLine,
          endLine: shouldIncludeImports ? endLine + (importEndLine + 2) : endLine, // Adjust for added separator lines
          type: this.mapSnippetType(identification.snippetType),
          symbols: [identification.snippetName],
          score: identification.relevanceScore,
          context: identification.reasoning,
          includesImports: shouldIncludeImports
        } as CodeSnippet);
      }
    }

    console.log('Total lines', results.reduce((acc, a) => {
      return acc + ((a.endLine - a.startLine)) + 1;
    }, 0));

    return orderBy(results, ['score'], ['desc']);
  }

  /**
   * Find the line where imports end in a file
   */
  private findImportEndLine(lines: string[]): number {
    let lastImportLine = 0;

    for (let i = 0; i < Math.min(lines.length, 50); i++) { // Only check first 50 lines
      const line = lines[i].trim();
      if (line.startsWith('import ')) {
        lastImportLine = i + 1; // +1 because line numbers are 1-based
      }
    }

    return lastImportLine;
  }

  /**
   * Main method to get relevant snippets for a query
   */
  async getRelevantSnippets(query: string, reason: string, excludedFiles: string[] = []): Promise<CodeSnippet[]> {
    // Stage 1: Find relevant files
    const {files: relevantFiles, reasoning} = await this.findRelevantFiles(query, reason, excludedFiles);

    if (relevantFiles.length === 0) {
      console.log("No relevant files found");
      return [];
    }

    // Stage 2: Identify relevant snippets within those files
    const snippetIdentifications = await this.identifyRelevantSnippets(query, reason, relevantFiles, reasoning);

    // Stage 3: Extract the actual snippets
    return this.extractSnippets(snippetIdentifications);
  }

  /**
   * Helper methods
   */
  private isCodeFile(fileName: string): boolean {
    const ext = path.extname(fileName).toLowerCase();
    return ['.ts', '.tsx', '.js', '.jsx'].includes(ext);
  }

  private determineFileType(fileName: string, content: string): string {
    const name = fileName.toLowerCase();

    if (name.includes('screen') || name.includes('page')) {
      return 'screen';
    }

    if (name.includes('context')) {
      return 'context';
    }

    if (name.includes('hook') || (content && content.includes('use') && content.includes('return {'))) {
      return 'hook';
    }

    if (name.includes('util') || name.includes('helper')) {
      return 'util';
    }

    if (name.includes('type') || name.includes('interface') || name.includes('.d.ts')) {
      return 'type';
    }

    if (name.includes('config') || name.includes('setup')) {
      return 'config';
    }

    // Default to component for TSX/JSX files
    if (name.endsWith('.tsx') || name.endsWith('.jsx')) {
      return 'component';
    }

    return 'unknown';
  }

  private extractExports(content: string): string[] {
    const exports: string[] = [];
    const exportRegex = /export\s+(const|function|class|interface|type|default)\s+(\w+)/g;

    let match;
    while ((match = exportRegex.exec(content)) !== null) {
      exports.push(match[2]);
    }

    return exports;
  }

  private extractImports(content: string): string[] {
    const imports: string[] = [];
    // Fixed regex to handle all import patterns including relative paths, scoped packages, and complex paths
    const importRegex = /import\s+(?:{[^}]*}|\*\s+as\s+\w+|\w+)\s+from\s+['"]([^'"]+)['"];?/g;

    let match;
    while ((match = importRegex.exec(content)) !== null) {
      const importPath = match[1];
      imports.push(importPath);
    }

    return imports;
  }

  /**
   * Resolve import path to actual file name in the project
   */
  private resolveImportPath(importPath: string, currentFile: string): string | null {
    // Skip node_modules and external packages (anything that doesn't start with ./ or ../)
    if (!importPath.startsWith('./') && !importPath.startsWith('../')) {
      return null;
    }

    // Handle relative paths only
    const currentDir = path.dirname(currentFile);
    let resolvedPath = path.join(currentDir, importPath);
    // Normalize path separators and remove leading ./
    resolvedPath = resolvedPath.replace(/\\/g, '/').replace(/^\.\//, '');

    // Try to find the actual file with common extensions
    const possibleExtensions = ['.ts', '.tsx', '.js', '.jsx', '/index.ts', '/index.tsx', '/index.js', '/index.jsx'];

    for (const ext of possibleExtensions) {
      const candidatePath = resolvedPath + ext;
      if (this.fileIndex.has(candidatePath)) {
        return candidatePath;
      }
    }

    // If no extension worked, try exact match
    if (this.fileIndex.has(resolvedPath)) {
      return resolvedPath;
    }

    return null;
  }

  private mapSnippetType(type: string): 'component' | 'hook' | 'screen' | 'util' | 'type' | 'context' | 'config' | 'unknown' {
    const normalizedType = type.toLowerCase();

    if (normalizedType.includes('component')) return 'component';
    if (normalizedType.includes('hook')) return 'hook';
    if (normalizedType.includes('screen')) return 'screen';
    if (normalizedType.includes('util') || normalizedType.includes('function')) return 'util';
    if (normalizedType.includes('type') || normalizedType.includes('interface')) return 'type';
    if (normalizedType.includes('context') || normalizedType.includes('provider')) return 'context';
    if (normalizedType.includes('config')) return 'config';

    return 'unknown';
  }
}
