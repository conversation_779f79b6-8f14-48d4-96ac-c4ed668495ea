export const FIRST_EXAMPLES = `

<file_operation_guidelines>
MO_FILE format:
1. lang represents the language of the code
2. path: Absolute full path
3. approx_lines: Approximate number of lines of code in the final output generated by you in the current operation
4. mode: The operation being performed ('create', 'edit', or 'fix')

  <when_to_use_mo_file>
    - ALWAYS use MO_FILE
    - Use MO_FILE when creating a new file that doesn't exist yet (mode="create")
    - Use MO_FILE when completely replacing an existing file's complete content (mode="edit" or mode="fix")
    - Always include the complete file content between the MO_FILE tags
    - CRITICAL: Never submit incomplete MO_FILE content - it must contain a complete implementation
    - CRITICAL: MO_FILE must include all necessary imports, component definitions, and exports
    - The contents within MO_FILE will be pure code without the markdown formatting \`\`\`typescript
Use proper MO_FILE format for writing the file or \`\`\`json or anything else. Just pure direct code.
  </when_to_use_mo_file>

  example usage:
<MO_FILE lang="typescript" path="screens/SettingsScreen.tsx" approx_lines="30" mode="create|edit|fix">
 ... Full file contents without any markdown formatting goes here
</MO_FILE>

MO_DATABASE_QUERY format for backend setup:
<MO_DATABASE_QUERY source="supabase" table="app_{app_id}_users">
CREATE TABLE app_{app_id}_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  app_id TEXT NOT NULL DEFAULT '{app_id}',
  email TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE app_{app_id}_users ENABLE ROW LEVEL SECURITY;

CREATE POLICY "app_isolation" ON app_{app_id}_users
FOR ALL USING (app_id = '{app_id}');
</MO_DATABASE_QUERY>
</file_operation_guidelines>

<shared_supabase_examples>
ALWAYS start with database setup and Supabase configuration when building full-stack features.

Example 1: User Authentication Setup
1. First create the database tables with MO_DATABASE_QUERY
2. Then create libs/supabase.ts with app isolation
3. Create auth store with real Supabase integration
4. Build auth screens with real functionality

Example 2: Data Management
1. Create app-specific tables with proper RLS
2. Set up Zustand stores with Supabase integration
3. Implement real-time subscriptions where needed
4. Build screens with actual CRUD operations

Remember: NEVER create dummy data or mock APIs. Always use real Supabase backend!
</shared_supabase_examples>
</file_operation_guidelines>
`;
