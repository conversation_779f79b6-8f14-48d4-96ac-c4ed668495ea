'use server';

import {generateUUID, slugify} from "@/lib/utils";
import {generateProjectAttributes} from "@/app/(project)/projects/actions";
import {Attachment, CoreUserMessage, Message} from "ai";
import {
    createOrFetchAnonUser,
    getMessageCountForToday,
    getProjectById,
    saveChat,
    saveMessages,
    saveProject
} from "@/lib/db/queries";
import {Message as DBMessage} from "@/lib/db/schema";
import {generateTitleFromUserMessage} from "@/app/(generator)/generator/actions";
import dayjs from "dayjs";
import {checkMessageLimit} from "@/lib/subscription";

export async function generateProject(message: string, userId: string, isAnonymous: boolean, attachments: Attachment[] = [], chatType: 'app' | 'design' = 'app') {

    try {
        const userMessage: CoreUserMessage = {
            content: message,
            role: 'user',
            attachments: attachments
        } as CoreUserMessage;

        if (isAnonymous) {
            const anonUser = await createOrFetchAnonUser(userId);
            // const count = await getMessageCountForToday(userId)
            // if(count >= 3) {
            //     throw new Error("429: Message limit for the day reached");
            // }

            const messageCheck = await checkMessageLimit(userId!, isAnonymous);

            if(!messageCheck.canSendMessage) {
                throw new Error("429: Message limit for the day reached");
            }
        }

        // Generate project attributes using AI
        const [existingProject, projectMetadata, title] = await Promise.all([
            getProjectById({
                id: 'de1c0f0a-55c2-4f26-b3db-bd3a167f0b60'
            }),
            generateProjectAttributes({
                messages: [userMessage] as Message[]
            }),
            generateTitleFromUserMessage({message: userMessage as CoreUserMessage})
        ])

        const projectId = generateUUID();
        const chatId = generateUUID();

        // Generate a unique slug based on the app name
        const baseSlug = slugify(projectMetadata.appName);
        const timestamp = Date.now().toString().slice(-6); // Use last 6 digits of timestamp for uniqueness
        const uniqueSlug = `${baseSlug}-${timestamp}`;

        // Save the project with the unique slug
        const project = await saveProject({
            id: projectId,
            userId: userId,
            slug: uniqueSlug,
            prompt: message,
            supabaseProjectId: existingProject.supabaseProjectId,
            supabaseAnonKey: existingProject.supabaseAnonKey,
            supabaseConnectionStatus: 'shared',
            connectionId: existingProject.connectionId,
            ...projectMetadata
        });

        const chat = await saveChat({
            id: chatId,
            userId,
            title,
            updatedAt: new Date(),
            projectId,
            isInitialized: false,
            type: chatType // Add the chat type parameter
        });
        const savedMessage = await saveMessages({
            messages: [{
                createdAt: dayjs().toDate(),
                userId: userId,
                projectId,
                chatId,
                role: userMessage.role,
                remoteProvider: null,
                remoteProviderId: null,
                // Format content as an array with text and image parts
                content: [
                    // First part is always the text message
                    { 
                        type: "text", 
                        text: userMessage.content 
                    },
                    // Add image parts for each attachment
                    ...attachments.map(attachment => ({
                        type: "image",
                        mimeType: attachment.contentType || "image/png",
                        image: attachment.url
                    }))
                ]
            } as DBMessage]
        });
        return {
            project,
            chat,
            message: savedMessage
        }
    } catch (e: any) {
        console.log('Error generating new project', e)
        throw e;
    }

}
